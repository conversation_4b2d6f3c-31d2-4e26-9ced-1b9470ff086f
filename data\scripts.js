// === CLEAN SECURITY SYSTEM ===
let allData = [];
let filteredData = [];
let currentPage = 1;
const rowsPerPage = 20;

// Security manager
const SecurityManager = {
    init: function() {
        this.setupBasicProtection();
        this.checkExtensions();
        this.validateSession();
    },

    setupBasicProtection: function() {
        // Disable right-click
        document.addEventListener('contextmenu', e => e.preventDefault());

        // Disable text selection
        document.addEventListener('selectstart', e => e.preventDefault());

        // Disable developer tools shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
                (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                return false;
            }
        });
    },

    checkExtensions: function() {
        // Simple extension detection
        setTimeout(() => {
            if (window.chrome && window.chrome.runtime) {
                this.showExtensionWarning();
            }
        }, 2000);
    },

    showExtensionWarning: function() {
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        warning.innerHTML = `
            <strong>⚠️ Extension Detected</strong><br>
            Please disable browser extensions for better security.
            <button onclick="this.parentElement.remove()" style="float: right; margin-left: 10px; background: white; color: #ff4444; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer;">×</button>
        `;
        document.body.appendChild(warning);

        // Log to server
        fetch('security.php?action=log_alert', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: 'alert=Extension detected'
        });

        setTimeout(() => warning.remove(), 10000);
    },

    validateSession: function() {
        fetch('security.php?action=validate_session')
            .then(response => response.json())
            .then(data => {
                if (data.valid) {
                    console.log('Security session validated');
                }
            })
            .catch(err => console.warn('Security validation failed'));
    }
};

// Initialize security on page load
document.addEventListener('DOMContentLoaded', () => SecurityManager.init());

// === SESSION MANAGEMENT FOR PAGE VIEW LIMITS ===
const SESSION_DURATION = 6 * 60 * 60 * 1000; // 6 hours in milliseconds
const MAX_PAGES_PER_SESSION = 5;

function initializeSession() {
    const now = Date.now();
    const sessionData = JSON.parse(localStorage.getItem('page_session') || '{}');

    // Check if session has expired
    if (!sessionData.startTime || (now - sessionData.startTime) > SESSION_DURATION) {
        // Start new session
        const newSession = {
            startTime: now,
            pagesViewed: 0,
            viewedPages: []
        };
        localStorage.setItem('page_session', JSON.stringify(newSession));
        return newSession;
    }

    return sessionData;
}

function canViewPage() {
    const session = initializeSession();
    return session.pagesViewed < MAX_PAGES_PER_SESSION;
}

function recordPageView() {
    const session = initializeSession();
    const currentPageKey = `${document.getElementById("countryFilter").value}_${document.getElementById("specialFilter").value}_${currentPage}`;

    // Only count if this specific page hasn't been viewed in this session
    if (!session.viewedPages.includes(currentPageKey)) {
        session.pagesViewed++;
        session.viewedPages.push(currentPageKey);
        localStorage.setItem('page_session', JSON.stringify(session));
    }

    return session;
}

function showSessionLimitMessage() {
    const session = initializeSession();
    const timeRemaining = SESSION_DURATION - (Date.now() - session.startTime);
    const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));

    alert(`You have reached your page view limit (${MAX_PAGES_PER_SESSION} pages per session). Please try again in ${hoursRemaining} hour(s).`);
}

function updateSessionInfo(session) {
    // Add session info to the page if element exists
    const sessionInfoEl = document.getElementById('sessionInfo');
    if (sessionInfoEl) {
        const remaining = MAX_PAGES_PER_SESSION - session.pagesViewed;
        sessionInfoEl.textContent = `Pages remaining this session: ${remaining}`;
    }
}

// === FETCH CONTACTS ===
async function fetchContacts() {
    // Check session limits before fetching
    if (!canViewPage()) {
        showSessionLimitMessage();
        return;
    }

    const country = document.getElementById("countryFilter").value;
    const filter = document.getElementById("specialFilter").value;

    const url = new URL("http://localhost/leadbase/Data/data.php");
    url.searchParams.append("action", "get_contacts");
    if (country) url.searchParams.append("country", country);
    if (filter && filter !== "none") {
        url.searchParams.append("special_filter", filter);
    }

    try {
        const res = await fetch(url);
        const json = await res.json();

        if (!json.success) throw new Error("Fetch failed");

        allData = json.data;
        filteredData = [...allData];
        currentPage = 1;

        // Record page view after successful fetch
        const session = recordPageView();

        renderTable();
        updateTotalResults();
        updateSessionInfo(session);
    } catch (err) {
        alert("Error fetching contacts: " + err.message);
        console.error(err);
    }
}

// === FETCH LIMITS USING EMAIL FROM LOCALSTORAGE ===
async function fetchUserLimits() {
    // Get user data from localStorage
    const meta = JSON.parse(localStorage.getItem("user_data") || "{}");
    const email = meta.email;
    const name = meta.name || "--";
    
    // Update avatar with first two letters of name
    document.getElementById("userAvatar").textContent = name.slice(0, 2).toUpperCase();
    
    if (!email) {
        console.warn("No email found in localStorage");
        return;
    }

    try {
        const res = await fetch(`http://localhost/leadbase/Data/data.php?action=get_limits&email=${encodeURIComponent(email)}`);
        const json = await res.json();
        if (json.success && json.limits) {
            userLimits.dailyLimit = json.limits.dailyLimit;
            userLimits.extraLimit = json.limits.extraLimit;
            document.getElementById("dailyLimit").textContent = json.limits.dailyLimit;
            document.getElementById("extraLimit").textContent = json.limits.extraLimit;
        } else {
            console.warn("Limit fetch failed:", json.error || "Unknown error");
        }
    } catch (err) {
        console.error("Failed to fetch limits:", err);
    }
}

// === SECURE TABLE RENDERING WITH CLEAR CANVAS ===
function renderTable() {
    const tbody = document.getElementById("tableBody");
    tbody.innerHTML = "";

    const start = (currentPage - 1) * rowsPerPage;
    const end = start + rowsPerPage;
    const pageData = filteredData.slice(start, end);

    pageData.forEach((row, index) => {
        const tr = document.createElement("tr");

        // Checkbox cell (normal)
        const checkboxCell = document.createElement("td");
        checkboxCell.innerHTML = '<input type="checkbox" class="checkbox row-checkbox">';
        tr.appendChild(checkboxCell);

        // Render sensitive data in canvas with clear, large fonts
        const nameCell = createClearCanvasCell(row.name || 'N/A', 'name');
        const phoneCell = createClearCanvasCell(row.phone || 'N/A', 'phone');
        const emailCell = createClearCanvasCell(row.email || 'N/A', 'email');
        const bioCell = createClearCanvasCell(row.bio || 'N/A', 'bio');

        tr.appendChild(nameCell);
        tr.appendChild(phoneCell);
        tr.appendChild(emailCell);
        tr.appendChild(bioCell);

        // Facebook cell (normal link)
        const facebookCell = document.createElement("td");
        if (row.fb_url) {
            facebookCell.innerHTML = `<a href="${row.fb_url}" class="facebook-link" target="_blank">Link</a>`;
        } else {
            facebookCell.appendChild(createClearCanvasCell('N/A', 'facebook'));
        }
        tr.appendChild(facebookCell);

        // Store original data for selection
        tr.setAttribute('data-original', JSON.stringify(row));

        tr.addEventListener("click", function (e) {
            if (!e.target.classList.contains("row-checkbox") && e.target.tagName !== 'A') {
                const cb = tr.querySelector(".row-checkbox");
                cb.checked = !cb.checked;
                tr.classList.toggle("selected", cb.checked);
                updateActionBar();
            }
        });

        tbody.appendChild(tr);
    });

    attachRowEvents();
    renderPagination();
}

// === CREATE CRYSTAL CLEAR CANVAS CELL ===
function createClearCanvasCell(text, type) {
    const cell = document.createElement("td");
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    // Get device pixel ratio for crisp rendering
    const dpr = window.devicePixelRatio || 1;
    const fontSize = 14;

    // Calculate dimensions
    const textWidth = Math.max(text.length * 8.5, 100);
    const textHeight = 24;

    // Set actual canvas size (scaled for device pixel ratio)
    canvas.width = textWidth * dpr;
    canvas.height = textHeight * dpr;

    // Set display size (CSS pixels)
    canvas.style.width = textWidth + 'px';
    canvas.style.height = textHeight + 'px';
    canvas.style.cssText = `
        width: ${textWidth}px;
        height: ${textHeight}px;
        cursor: pointer;
        border: none;
        background: transparent;
        display: block;
        max-width: 300px;
    `;

    // Scale the context to match device pixel ratio
    ctx.scale(dpr, dpr);

    // Enable text smoothing
    ctx.textRenderingOptimization = 'optimizeQuality';
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Set font with proper rendering
    ctx.font = `${fontSize}px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif`;
    ctx.fillStyle = "#333333";
    ctx.textAlign = "left";
    ctx.textBaseline = "middle";

    // Clear background with white
    ctx.fillStyle = "#ffffff";
    ctx.fillRect(0, 0, textWidth, textHeight);

    // Draw text with proper color
    ctx.fillStyle = "#333333";
    ctx.fillText(text, 6, textHeight / 2);

    // Store original text for selection functionality
    canvas.setAttribute('data-secure-text', btoa(encodeURIComponent(text)));
    canvas.setAttribute('data-type', type);
    canvas.setAttribute('data-original-text', text);

    // Prevent canvas data extraction
    canvas.toDataURL = function() {
        console.warn('Canvas data extraction blocked');
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    };

    canvas.toBlob = function() {
        console.warn('Canvas blob extraction blocked');
        return null;
    };

    // Block getImageData
    const originalGetImageData = ctx.getImageData;
    ctx.getImageData = function() {
        console.warn('Canvas getImageData blocked');
        return originalGetImageData.call(this, 0, 0, 1, 1);
    };

    cell.appendChild(canvas);
    return cell;
}

// === DYNAMIC SCRIPT ENCRYPTION ===
const SecurityCore = {
    // Simple XOR encryption for obfuscation
    encrypt: (text, key = 'LeadBaseSecure2024') => {
        let result = '';
        for (let i = 0; i < text.length; i++) {
            result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
        }
        return btoa(result);
    },

    decrypt: (encrypted, key = 'LeadBaseSecure2024') => {
        const text = atob(encrypted);
        let result = '';
        for (let i = 0; i < text.length; i++) {
            result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
        }
        return result;
    },

    // Obfuscated function names
    _0x9a8b: function(data) { // Original: getSelectedContacts
        const selected = [];
        document.querySelectorAll('.row-checkbox:checked').forEach(checkbox => {
            const row = checkbox.closest('tr');
            const originalData = row.getAttribute('data-original');
            if (originalData) {
                selected.push(JSON.parse(originalData));
            }
        });
        return selected;
    },

    // Anti-tampering check
    integrity: function() {
        const expectedFunctions = ['renderTable', 'fetchContacts', 'updateUserLimits'];
        for (const func of expectedFunctions) {
            if (typeof window[func] !== 'function') {
                console.error('Security violation: Core function missing');
                document.body.innerHTML = '<h1 style="text-align:center;color:red;">Security Error</h1>';
                return false;
            }
        }
        return true;
    },

    // Runtime protection
    protect: function() {
        // Disable common debugging methods
        const noop = () => {};
        window.console.log = noop;
        window.console.warn = noop;
        window.console.error = noop;
        window.console.info = noop;
        window.console.debug = noop;

        // Override eval to prevent code injection
        window.eval = function() {
            console.warn('eval() blocked for security');
            return null;
        };

        // Block Function constructor
        window.Function = function() {
            console.warn('Function constructor blocked');
            return function() {};
        };

        // Periodic integrity check
        setInterval(() => {
            if (!this.integrity()) {
                location.reload();
            }
        }, 30000);
    }
};

// Initialize security
SecurityCore.protect();

// === CREATE SECURE CELLS ===
function createSecureCells(row, index) {
    const cells = {};

    // Checkbox cell
    cells.checkbox = document.createElement("td");
    const checkbox = document.createElement("input");
    checkbox.type = "checkbox";
    checkbox.className = "checkbox row-checkbox";
    cells.checkbox.appendChild(checkbox);

    // Name cell with obfuscation
    cells.name = document.createElement("td");
    cells.name.appendChild(createObfuscatedContent(row.name || 'N/A', 'name'));

    // Phone cell with obfuscation
    cells.phone = document.createElement("td");
    cells.phone.appendChild(createObfuscatedContent(row.phone || 'N/A', 'phone'));

    // Email cell with obfuscation
    cells.email = document.createElement("td");
    cells.email.appendChild(createObfuscatedContent(row.email || 'N/A', 'email'));

    // Bio cell with obfuscation
    cells.bio = document.createElement("td");
    cells.bio.appendChild(createObfuscatedContent(row.bio || 'N/A', 'bio'));

    // Facebook cell
    cells.facebook = document.createElement("td");
    if (row.fb_url) {
        const link = document.createElement("a");
        link.href = row.fb_url;
        link.textContent = "Link";
        link.className = "facebook-link";
        link.target = "_blank";
        cells.facebook.appendChild(link);
    } else {
        cells.facebook.appendChild(createObfuscatedContent('N/A', 'facebook'));
    }

    return cells;
}

// === CREATE OBFUSCATED CONTENT ===
function createObfuscatedContent(realData, dataType) {
    const container = document.createElement("div");
    container.className = `data-container data-${dataType}`;

    // Split real data into character spans for advanced protection
    const visibleContainer = document.createElement("span");
    visibleContainer.className = "visible-data";

    // Create character-by-character spans with decoy characters
    const chars = realData.split('');
    chars.forEach((char, index) => {
        const charSpan = document.createElement("span");
        charSpan.className = `char-${index}`;
        charSpan.textContent = char;

        // Add invisible decoy characters between real characters
        if (index < chars.length - 1) {
            const decoyChar = document.createElement("span");
            decoyChar.className = "decoy-char";
            decoyChar.style.display = "none";
            decoyChar.style.fontSize = "0px";
            decoyChar.style.opacity = "0";
            decoyChar.textContent = String.fromCharCode(65 + Math.floor(Math.random() * 26));
            visibleContainer.appendChild(charSpan);
            visibleContainer.appendChild(decoyChar);
        } else {
            visibleContainer.appendChild(charSpan);
        }
    });

    // Create multiple decoy spans with fake data
    for (let i = 0; i < 8; i++) {
        const decoySpan = document.createElement("span");
        decoySpan.className = "decoy-data";
        decoySpan.style.display = "none";
        decoySpan.style.position = "absolute";
        decoySpan.style.left = "-9999px";
        decoySpan.setAttribute('data-fake', 'true');
        decoySpan.textContent = generateFakeData(dataType);
        container.appendChild(decoySpan);
    }

    container.appendChild(visibleContainer);

    // Add more decoy spans after visible content
    for (let i = 0; i < 5; i++) {
        const decoySpan = document.createElement("span");
        decoySpan.className = "decoy-data-after";
        decoySpan.style.display = "none";
        decoySpan.style.position = "absolute";
        decoySpan.style.left = "-9999px";
        decoySpan.setAttribute('data-fake', 'true');
        decoySpan.textContent = generateFakeData(dataType);
        container.appendChild(decoySpan);
    }

    // Store real data in a protected way
    container.setAttribute('data-protected', btoa(encodeURIComponent(realData)));
    container.setAttribute('data-type', dataType);

    return container;
}

// === GENERATE FAKE DATA ===
function generateFakeData(dataType) {
    const fakeData = {
        name: ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Mike Wilson'],
        phone: ['******-0123', '******-0456', '******-0789', '******-0321', '******-0654'],
        email: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        bio: ['Software Engineer', 'Marketing Manager', 'Sales Director', 'Product Manager', 'Data Analyst'],
        facebook: ['N/A', 'Link', 'Profile', 'Page', 'Account']
    };

    const options = fakeData[dataType] || ['Fake Data'];
    return options[Math.floor(Math.random() * options.length)];
}

// === ADD DECOY ROWS ===
function addDecoyRows(tbody) {
    const decoyCount = Math.floor(Math.random() * 3) + 2; // 2-4 decoy rows

    for (let i = 0; i < decoyCount; i++) {
        const decoyRow = document.createElement("tr");
        decoyRow.style.display = "none";
        decoyRow.className = "decoy-row";
        decoyRow.setAttribute('data-fake', 'true');

        // Add fake cells
        for (let j = 0; j < 6; j++) {
            const cell = document.createElement("td");
            if (j === 0) {
                const checkbox = document.createElement("input");
                checkbox.type = "checkbox";
                checkbox.className = "fake-checkbox";
                cell.appendChild(checkbox);
            } else {
                cell.textContent = generateFakeData(['name', 'phone', 'email', 'bio', 'facebook'][j-1]);
            }
            decoyRow.appendChild(cell);
        }

        tbody.appendChild(decoyRow);
    }
}

// === DYNAMIC OBFUSCATION ===
function applyDynamicObfuscation() {
    const allCells = document.querySelectorAll('#contactTable td');

    allCells.forEach(cell => {
        // Add random attributes to confuse scrapers
        cell.setAttribute('data-hash', btoa(Math.random().toString()));
        cell.setAttribute('data-index', Math.floor(Math.random() * 1000));
        cell.setAttribute('data-type', ['text', 'data', 'info', 'content'][Math.floor(Math.random() * 4)]);

        // Add invisible text nodes
        const textNode = document.createTextNode('');
        cell.appendChild(textNode);
    });
}

// === GENERATE FAKE ID ===
function generateFakeId() {
    return 'id_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
}

// === ACTIVE CONTENT PROTECTION ===
function startActiveProtection() {
    let protectionActive = false;

    // Monitor for scraping activity
    const activityMonitor = setInterval(() => {
        const suspiciousActivity = detectScrapingActivity();

        if (suspiciousActivity && !protectionActive) {
            console.warn('Scraping activity detected - activating content protection');
            protectionActive = true;
            replaceVisibleContentWithFakes();

            // Restore real content after 5 seconds
            setTimeout(() => {
                restoreRealContent();
                protectionActive = false;
            }, 5000);
        }
    }, 1000);

    // Also activate protection randomly to confuse scrapers
    setInterval(() => {
        if (!protectionActive && Math.random() < 0.1) { // 10% chance every 10 seconds
            protectionActive = true;
            replaceVisibleContentWithFakes();
            setTimeout(() => {
                restoreRealContent();
                protectionActive = false;
            }, 2000);
        }
    }, 10000);
}

// === ADVANCED AUTHORIZATION SYSTEM ===
function isAuthorizedAccess() {
    // Check multiple factors to determine if access is legitimate

    // 1. User authentication check
    const userData = JSON.parse(localStorage.getItem("user_data") || "{}");
    if (!userData.email) {
        console.warn('No authenticated user found');
        return false;
    }

    // 2. Session validation
    const sessionData = JSON.parse(localStorage.getItem('page_session') || '{}');
    if (!sessionData.startTime) {
        console.warn('No valid session found');
        return false;
    }

    // 3. Check for legitimate user interaction patterns
    if (!hasLegitimateUserInteraction()) {
        console.warn('No legitimate user interaction detected');
        return false;
    }

    // 4. Browser environment validation
    if (isSuspiciousBrowserEnvironment()) {
        console.warn('Suspicious browser environment detected');
        return false;
    }

    return true;
}

// === DETECT SCRAPING ACTIVITY ===
function detectScrapingActivity() {
    // First check if access is authorized
    if (!isAuthorizedAccess()) {
        return true; // Treat unauthorized access as scraping
    }

    // Check for rapid DOM access patterns
    const now = Date.now();
    if (!window.lastDOMAccess) window.lastDOMAccess = [];

    window.lastDOMAccess.push(now);
    window.lastDOMAccess = window.lastDOMAccess.filter(time => now - time < 5000);

    // More lenient threshold for authorized users
    return window.lastDOMAccess.length > 50; // Increased from 20 to 50
}

// === CHECK LEGITIMATE USER INTERACTION ===
function hasLegitimateUserInteraction() {
    // Check if user has performed legitimate actions
    const userActions = window.userActionLog || [];
    const recentActions = userActions.filter(action => Date.now() - action.timestamp < 30000); // Last 30 seconds

    // Look for human-like interaction patterns
    const hasClicks = recentActions.some(action => action.type === 'click');
    const hasScrolls = recentActions.some(action => action.type === 'scroll');
    const hasKeyboard = recentActions.some(action => action.type === 'keyboard');

    return hasClicks || hasScrolls || hasKeyboard;
}

// === DETECT SUSPICIOUS BROWSER ENVIRONMENT ===
function isSuspiciousBrowserEnvironment() {
    // Check for automation indicators
    if (navigator.webdriver) return true;
    if (window.phantom || window._phantom) return true;
    if (window.callPhantom) return true;

    // Check for headless browser indicators
    if (navigator.languages && navigator.languages.length === 0) return true;
    if (navigator.plugins && navigator.plugins.length === 0) return true;

    // Check for extension environment (but be more specific)
    if (window.chrome && window.chrome.runtime && !window.chrome.app) {
        // This might be a content script, but not necessarily malicious
        return false; // Don't automatically flag as suspicious
    }

    return false;
}

// === LOG USER ACTIONS ===
function logUserAction(type, details = {}) {
    if (!window.userActionLog) window.userActionLog = [];

    window.userActionLog.push({
        type: type,
        timestamp: Date.now(),
        details: details
    });

    // Keep only last 100 actions
    if (window.userActionLog.length > 100) {
        window.userActionLog = window.userActionLog.slice(-100);
    }
}

// === WHITELIST SYSTEM ===
function isWhitelistedAccess() {
    // Check if the access comes from trusted sources

    // 1. Check referrer
    const referrer = document.referrer;
    const trustedDomains = ['localhost', '127.0.0.1', 'leadbaseai.in'];
    const currentDomain = window.location.hostname;

    if (!trustedDomains.includes(currentDomain)) {
        console.warn('Access from untrusted domain:', currentDomain);
        return false;
    }

    // 2. Check user agent patterns (basic check)
    const userAgent = navigator.userAgent;
    const suspiciousPatterns = [
        'headless', 'phantom', 'selenium', 'webdriver',
        'bot', 'crawler', 'spider', 'scraper'
    ];

    for (const pattern of suspiciousPatterns) {
        if (userAgent.toLowerCase().includes(pattern)) {
            console.warn('Suspicious user agent detected:', pattern);
            return false;
        }
    }

    // 3. Check for legitimate browser features
    if (!window.localStorage || !window.sessionStorage) {
        console.warn('Missing browser storage features');
        return false;
    }

    return true;
}

// === TRUST SCORE SYSTEM ===
function calculateTrustScore() {
    let score = 0;

    // Base score for authenticated user
    const userData = JSON.parse(localStorage.getItem("user_data") || "{}");
    if (userData.email) score += 30;

    // Score for user interactions
    const userActions = window.userActionLog || [];
    const recentActions = userActions.filter(action => Date.now() - action.timestamp < 60000);

    score += Math.min(recentActions.length * 2, 40); // Max 40 points for interactions

    // Score for session age (older sessions are more trusted)
    const sessionData = JSON.parse(localStorage.getItem('page_session') || '{}');
    if (sessionData.startTime) {
        const sessionAge = Date.now() - sessionData.startTime;
        const ageMinutes = sessionAge / (1000 * 60);
        score += Math.min(ageMinutes * 2, 20); // Max 20 points for session age
    }

    // Score for whitelist status
    if (isWhitelistedAccess()) score += 10;

    return Math.min(score, 100); // Cap at 100
}

// === ENHANCED AUTHORIZATION CHECK ===
function isAuthorizedAccessEnhanced() {
    const trustScore = calculateTrustScore();

    console.log('Trust Score:', trustScore);

    // Require minimum trust score of 50 for full access
    if (trustScore < 50) {
        console.warn('Low trust score:', trustScore);
        return false;
    }

    // Additional checks for high-risk operations
    if (trustScore < 70) {
        // Allow limited access but with extra monitoring
        console.warn('Medium trust score - enhanced monitoring active');
        return 'limited';
    }

    return true; // Full access
}

// === REPLACE VISIBLE CONTENT WITH FAKES ===
function replaceVisibleContentWithFakes() {
    const visibleDataElements = document.querySelectorAll('.visible-data');

    visibleDataElements.forEach(element => {
        if (!element.getAttribute('data-original')) {
            // Store original content
            element.setAttribute('data-original', element.textContent);

            // Replace with fake data
            const parentContainer = element.closest('.data-container');
            const dataType = parentContainer ? parentContainer.getAttribute('data-type') : 'bio';
            element.textContent = generateFakeData(dataType);
            element.setAttribute('data-fake-active', 'true');
        }
    });
}

// === RESTORE REAL CONTENT ===
function restoreRealContent() {
    const fakeElements = document.querySelectorAll('.visible-data[data-fake-active="true"]');

    fakeElements.forEach(element => {
        const originalContent = element.getAttribute('data-original');
        if (originalContent) {
            element.textContent = originalContent;
            element.removeAttribute('data-original');
            element.removeAttribute('data-fake-active');
        }
    });
}

/// === MINIMAL PAGINATION ===
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / rowsPerPage);
    const start = (currentPage - 1) * rowsPerPage + 1;
    const end = Math.min(currentPage * rowsPerPage, filteredData.length);

    // Optional: show counts (if you kept #paginationInfo)
    const infoEl = document.getElementById("paginationInfo");
    if (infoEl) {
        infoEl.textContent = `Showing ${start}-${end} of ${filteredData.length} contacts`;
    }

    // Update current page display
    const currentDisplay = document.getElementById("currentPageDisplay");
    if (currentDisplay) {
        currentDisplay.textContent = `Page ${currentPage}`;
    }

    // Prev button
    const prevBtn = document.getElementById("prevBtn");
    if (prevBtn) {
        prevBtn.disabled = currentPage === 1;
        prevBtn.onclick = () => {
            if (currentPage > 1) {
                if (!canViewPage()) {
                    showSessionLimitMessage();
                    return;
                }
                currentPage--;
                const session = recordPageView();
                renderTable();
                updateSessionInfo(session);
            }
        };
    }

    // Next button
    const nextBtn = document.getElementById("nextBtn");
    if (nextBtn) {
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.onclick = () => {
            if (currentPage < totalPages) {
                if (!canViewPage()) {
                    showSessionLimitMessage();
                    return;
                }
                currentPage++;
                const session = recordPageView();
                renderTable();
                updateSessionInfo(session);
            }
        };
    }
}

// === ROW EVENT BINDING ===
function attachRowEvents() {
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function () {
            this.closest('tr').classList.toggle('selected', this.checked);
            updateActionBar();
        });
    });

    document.getElementById('selectAll').addEventListener('change', function () {
        rowCheckboxes.forEach(cb => {
            cb.checked = this.checked;
            cb.closest('tr').classList.toggle('selected', this.checked);
        });
        updateActionBar();
    });
}

// === ACTION BAR ===
function updateActionBar() {
    const count = document.querySelectorAll('.row-checkbox:checked').length;
    const bar = document.getElementById("actionBar");
    document.getElementById("selectedCount").textContent = count;
    bar.classList.toggle("show", count > 0);
}

// === SEARCH ===
function performSearch() {
    const term = document.getElementById("searchInput").value.toLowerCase();

    if (term === "") {
        filteredData = [...allData];
    } else {
        filteredData = allData.filter(row =>
            (row.name && row.name.toLowerCase().includes(term)) ||
            (row.phone && row.phone.toLowerCase().includes(term)) ||
            (row.email && row.email.toLowerCase().includes(term)) ||
            (row.bio && row.bio.toLowerCase().includes(term))
        );
    }

    currentPage = 1;
    renderTable();
    updateTotalResults();
}

// === TOTAL RESULTS ===
function updateTotalResults() {
    document.getElementById("totalResults").textContent = filteredData.length;
}

// === PROFILE INIT ===
document.addEventListener("DOMContentLoaded", () => {
    const meta = JSON.parse(localStorage.getItem("user_data") || "{}");
    const name = meta.name || "--";
    const streak = meta.streak ?? 0;

    // Display first two letters of name in avatar (already done in fetchUserLimits, but keeping here for safety)
    document.getElementById("userAvatar").textContent = name.slice(0, 2).toUpperCase();
    document.getElementById("streakBox").textContent = `🔥 Streak: ${streak}`;

    const avatar = document.getElementById("userAvatar");
    const menu = document.getElementById("profileMenu");
    avatar.addEventListener("click", () => {
        menu.style.display = menu.style.display === "none" ? "block" : "none";
        menu.innerHTML = `<div>Hello, ${name}</div>`;
    });
    document.body.addEventListener("click", e => {
        if (!avatar.contains(e.target) && !menu.contains(e.target)) {
            menu.style.display = "none";
        }
    });
});

// === DOWNLOAD AND SAVE FUNCTIONALITY ===
let userLimits = { dailyLimit: 0, extraLimit: 0 };

async function updateUserLimits(email, newDailyLimit, newExtraLimit) {
    console.log('Updating limits for:', email, 'Daily:', newDailyLimit, 'Extra:', newExtraLimit);

    try {
        const formData = new FormData();
        formData.append('action', 'update_limits');
        formData.append('email', email);
        formData.append('dailyLimit', newDailyLimit);
        formData.append('extraLimit', newExtraLimit);

        const response = await fetch('http://localhost/leadbase/Data/data.php', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);

        const result = await response.json();
        console.log('Update response:', result);

        if (!result.success) {
            console.error('Update failed:', result.error);
            alert('Failed to update limits: ' + (result.error || 'Unknown error'));
        }

        return result.success;
    } catch (error) {
        console.error('Error updating limits:', error);
        alert('Network error while updating limits: ' + error.message);
        return false;
    }
}

function getSelectedContacts() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    const selectedData = [];

    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const originalData = row.getAttribute('data-original');
        if (originalData) {
            selectedData.push(JSON.parse(originalData));
        } else {
            // Fallback to canvas data extraction
            const cells = row.querySelectorAll('td');
            if (cells.length >= 6) {
                const getCanvasText = (cell) => {
                    const canvas = cell.querySelector('canvas');
                    if (canvas) {
                        return canvas.getAttribute('data-original-text') || 'N/A';
                    }
                    return cell.textContent.trim();
                };

                selectedData.push({
                    name: getCanvasText(cells[1]),
                    phone: getCanvasText(cells[2]),
                    email: getCanvasText(cells[3]),
                    bio: getCanvasText(cells[4]),
                    facebook: getCanvasText(cells[5])
                });
            }
        }
    });

    return selectedData;
}

async function downloadContacts() {
    const selectedContacts = getSelectedContacts();

    if (selectedContacts.length === 0) {
        alert('Please select contacts to download.');
        return;
    }

    const totalAvailableLimit = userLimits.dailyLimit + userLimits.extraLimit;

    if (selectedContacts.length > totalAvailableLimit) {
        alert(`You can only download ${totalAvailableLimit} contacts. You selected ${selectedContacts.length}.`);
        return;
    }

    // Calculate new limits after download
    let newDailyLimit = userLimits.dailyLimit;
    let newExtraLimit = userLimits.extraLimit;
    let remainingToDeduct = selectedContacts.length;

    // First deduct from daily limit
    if (remainingToDeduct > 0 && newDailyLimit > 0) {
        const deductFromDaily = Math.min(remainingToDeduct, newDailyLimit);
        newDailyLimit -= deductFromDaily;
        remainingToDeduct -= deductFromDaily;
    }

    // Then deduct from extra limit
    if (remainingToDeduct > 0 && newExtraLimit > 0) {
        const deductFromExtra = Math.min(remainingToDeduct, newExtraLimit);
        newExtraLimit -= deductFromExtra;
        remainingToDeduct -= deductFromExtra;
    }

    // Get user email from localStorage
    const userData = JSON.parse(localStorage.getItem("user_data") || "{}");
    const email = userData.email;

    console.log('User data from localStorage:', userData);
    console.log('Email for update:', email);

    if (!email) {
        alert('User email not found in localStorage. Please log in again.');
        console.error('No email found in user_data:', userData);
        return;
    }

    // Update limits in database
    console.log('About to update limits in database...');
    const updateSuccess = await updateUserLimits(email, newDailyLimit, newExtraLimit);

    if (!updateSuccess) {
        console.error('Failed to update limits in database');
        alert('Failed to update limits in database. Download cancelled.');
        return;
    }

    console.log('Limits updated successfully in database');

    // Update local limits
    userLimits.dailyLimit = newDailyLimit;
    userLimits.extraLimit = newExtraLimit;

    // Update UI
    document.getElementById("dailyLimit").textContent = newDailyLimit;
    document.getElementById("extraLimit").textContent = newExtraLimit;

    // Create and download Excel file
    const worksheet = XLSX.utils.json_to_sheet(selectedContacts);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Contacts");

    const fileName = `contacts_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    alert(`Downloaded ${selectedContacts.length} contacts successfully!`);

    // Clear selections
    document.querySelectorAll('.row-checkbox:checked').forEach(cb => cb.checked = false);
    document.querySelectorAll('tr.selected').forEach(tr => tr.classList.remove('selected'));
    updateActionBar();
}

function saveContacts() {
    const selectedContacts = getSelectedContacts();

    if (selectedContacts.length === 0) {
        alert('Please select contacts to save.');
        return;
    }

    // Get existing saved contacts
    const savedContacts = JSON.parse(localStorage.getItem('savedContacts') || '[]');

    // Add new contacts (avoid duplicates based on email)
    let addedCount = 0;
    selectedContacts.forEach(contact => {
        const exists = savedContacts.some(saved => saved.email === contact.email && contact.email !== 'N/A');
        if (!exists) {
            savedContacts.push({
                ...contact,
                savedDate: new Date().toISOString()
            });
            addedCount++;
        }
    });

    // Save to localStorage
    localStorage.setItem('savedContacts', JSON.stringify(savedContacts));

    alert(`Saved ${addedCount} new contacts. ${selectedContacts.length - addedCount} were already saved.`);

    // Clear selections
    document.querySelectorAll('.row-checkbox:checked').forEach(cb => cb.checked = false);
    document.querySelectorAll('tr.selected').forEach(tr => tr.classList.remove('selected'));
    updateActionBar();
}

// === FILTERS ===
document.getElementById("searchInput").addEventListener("input", performSearch);
document.getElementById("countryFilter").addEventListener("change", fetchContacts);
document.getElementById("specialFilter").addEventListener("change", fetchContacts);

// === ACTION BAR EVENTS ===
document.getElementById("downloadBtn").addEventListener("click", downloadContacts);
document.getElementById("saveBtn").addEventListener("click", saveContacts);

// === SIMPLE CONTENT PROTECTION ===
function initContentProtection() {
    // Disable right-click context menu
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable text selection
    document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable drag
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable developer tools shortcuts
    document.addEventListener('keydown', function(e) {
        // Disable F12 (Developer Tools)
        if (e.key === 'F12') {
            e.preventDefault();
            return false;
        }

        // Disable Ctrl+Shift+I (Developer Tools)
        if (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i')) {
            e.preventDefault();
            return false;
        }

        // Disable Ctrl+Shift+J (Console)
        if (e.ctrlKey && e.shiftKey && (e.key === 'J' || e.key === 'j')) {
            e.preventDefault();
            return false;
        }

        // Disable Ctrl+U (View Source)
        if (e.ctrlKey && (e.key === 'u' || e.key === 'U')) {
            e.preventDefault();
            return false;
        }
    });

    // Basic extension detection
    detectBasicExtensions();

    // Add CSS to prevent text selection
    const style = document.createElement('style');
    style.textContent = `
        * {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            -webkit-touch-callout: none !important;
        }

        input, textarea, select {
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            user-select: text !important;
        }

        /* Prevent highlighting */
        ::selection { background: transparent !important; }
        ::-moz-selection { background: transparent !important; }
    `;
    document.head.appendChild(style);
}

// === BASIC EXTENSION DETECTION ===
function detectBasicExtensions() {
    // Simple check for common scraping extensions
    setTimeout(() => {
        // Check for extension-injected elements
        const suspiciousElements = document.querySelectorAll('[class*="extension"], [id*="extension"], [data-extension]');
        if (suspiciousElements.length > 0) {
            console.warn('Potential browser extension detected');
            showExtensionWarning();
        }

        // Check for common extension signatures
        if (window.chrome && window.chrome.runtime) {
            console.warn('Chrome extension environment detected');
            showExtensionWarning();
        }
    }, 3000); // Check after 3 seconds
}

// === SHOW EXTENSION WARNING ===
function showExtensionWarning() {
    const warningDiv = document.createElement('div');
    warningDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px;
        border-radius: 5px;
        z-index: 9999;
        font-size: 14px;
        max-width: 300px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    `;
    warningDiv.innerHTML = `
        <strong>⚠️ Security Notice</strong><br>
        Browser extension detected. Please disable extensions for better security.
        <button onclick="this.parentElement.remove()" style="float: right; margin-left: 10px; background: white; color: #ff4444; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer;">×</button>
    `;
    document.body.appendChild(warningDiv);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (warningDiv.parentNode) {
            warningDiv.parentNode.removeChild(warningDiv);
        }
    }, 10000);
}

// === ADVANCED DOM SCRAPING PROTECTION ===
function protectAgainstDOMScraping() {
    // Store original DOM methods
    const originalQuerySelector = document.querySelector;
    const originalQuerySelectorAll = document.querySelectorAll;
    const originalGetElementById = document.getElementById;
    const originalGetElementsByClassName = document.getElementsByClassName;
    const originalGetElementsByTagName = document.getElementsByTagName;
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    const originalTextContent = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent');

    // Monitor suspicious DOM access patterns
    let domAccessCount = 0;
    const maxDomAccess = 100; // Threshold for suspicious activity

    function checkSuspiciousActivity() {
        domAccessCount++;
        if (domAccessCount > maxDomAccess) {
            console.warn('Suspicious DOM access detected');
            // Could implement additional blocking here
        }
    }

    // Override DOM selection methods with monitoring
    document.querySelector = function(selector) {
        checkSuspiciousActivity();
        // Block access to sensitive table data
        if (selector && (selector.includes('table') || selector.includes('tbody') || selector.includes('tr') || selector.includes('td'))) {
            console.warn('Blocked table access attempt');
            return null;
        }
        return originalQuerySelector.call(this, selector);
    };

    document.querySelectorAll = function(selector) {
        checkSuspiciousActivity();
        // Block access to sensitive table data
        if (selector && (selector.includes('table') || selector.includes('tbody') || selector.includes('tr') || selector.includes('td'))) {
            console.warn('Blocked table access attempt');
            return [];
        }
        return originalQuerySelectorAll.call(this, selector);
    };

    // Override innerHTML to return fake data for table elements
    Object.defineProperty(Element.prototype, 'innerHTML', {
        get: function() {
            if (this.tagName === 'TD' || this.tagName === 'TR' || this.tagName === 'TBODY') {
                checkSuspiciousActivity();
                console.warn('Blocked innerHTML access on table element');
                return generateFakeData('bio'); // Return fake data
            }
            return originalInnerHTML.get.call(this);
        },
        set: originalInnerHTML.set,
        configurable: false
    });

    // Override textContent to return fake data for table elements
    Object.defineProperty(Node.prototype, 'textContent', {
        get: function() {
            if (this.tagName === 'TD' || this.tagName === 'TR' || this.tagName === 'TBODY') {
                checkSuspiciousActivity();
                console.warn('Blocked textContent access on table element');
                return generateFakeData('bio'); // Return fake data
            }
            // Special handling for data containers
            if (this.classList && this.classList.contains('data-container')) {
                checkSuspiciousActivity();
                console.warn('Blocked data container access');
                return generateFakeData('bio');
            }
            if (this.classList && this.classList.contains('visible-data')) {
                checkSuspiciousActivity();
                console.warn('Blocked visible data access');
                return generateFakeData('bio');
            }
            return originalTextContent.get.call(this);
        },
        set: originalTextContent.set,
        configurable: false
    });

    // Override innerText as well
    Object.defineProperty(HTMLElement.prototype, 'innerText', {
        get: function() {
            if (this.tagName === 'TD' || this.tagName === 'TR' || this.tagName === 'TBODY') {
                checkSuspiciousActivity();
                console.warn('Blocked innerText access on table element');
                return generateFakeData('bio');
            }
            if (this.classList && (this.classList.contains('data-container') || this.classList.contains('visible-data'))) {
                checkSuspiciousActivity();
                console.warn('Blocked innerText access on protected element');
                return generateFakeData('bio');
            }
            return this.textContent;
        },
        configurable: false
    });

    // Protect against extension content scripts
    Object.defineProperty(document, 'body', {
        get: function() {
            checkSuspiciousActivity();
            return document.getElementsByTagName('body')[0];
        },
        configurable: false
    });

    // Block common scraping libraries detection
    if (window.jQuery || window.$ || window.cheerio || window.puppeteer) {
        console.warn('Scraping library detected');
    }

    // Detect automation tools
    if (navigator.webdriver || window.phantom || window._phantom || window.callPhantom) {
        console.warn('Automation tool detected');
        document.body.innerHTML = '<h1>Access Denied</h1><p>Automated access is not allowed.</p>';
        return;
    }

    // Protect against clipboard access
    document.addEventListener('copy', function(e) {
        e.clipboardData.setData('text/plain', 'Copying is disabled');
        e.preventDefault();
        return false;
    });

    // Monitor for rapid DOM queries (typical of scrapers)
    let queryCount = 0;
    const queryInterval = setInterval(() => {
        if (queryCount > 50) { // More than 50 queries per second
            console.warn('Rapid DOM querying detected - possible scraper');
            clearInterval(queryInterval);
        }
        queryCount = 0;
    }, 1000);

    // Obfuscate table data periodically
    setInterval(() => {
        obfuscateTableData();
        scrambleTableAttributes();
    }, 15000); // Every 15 seconds

    // Add immediate protection
    protectTableData();
}

// === SCRAMBLE TABLE ATTRIBUTES ===
function scrambleTableAttributes() {
    const tableRows = document.querySelectorAll('#contactTable tbody tr:not(.decoy-row)');
    tableRows.forEach((row, index) => {
        // Constantly change attributes to confuse scrapers
        row.setAttribute('data-scrambled', btoa(Math.random().toString()));
        row.setAttribute('data-position', Math.floor(Math.random() * 1000));
        row.setAttribute('data-timestamp', Date.now() + Math.random());

        const cells = row.querySelectorAll('td');
        cells.forEach((cell, cellIndex) => {
            cell.setAttribute('data-fake-content', generateFakeData(['name', 'phone', 'email', 'bio', 'facebook'][cellIndex] || 'bio'));
            cell.setAttribute('data-decoy', generateFakeData('bio'));
            cell.setAttribute('data-cell-id', Math.random().toString(36).substring(2, 15));
        });
    });
}

// === PROTECT TABLE DATA ===
function protectTableData() {
    const table = document.getElementById('contactTable');
    if (!table) return;

    // Add mutation observer to detect scraping attempts
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-scraped') {
                console.warn('Scraping attempt detected!');
                // Immediately scramble data
                scrambleTableAttributes();
                // Add more decoy rows
                addDecoyRows(document.getElementById('tableBody'));
            }
        });
    });

    observer.observe(table, {
        attributes: true,
        subtree: true,
        attributeFilter: ['data-scraped', 'class', 'id']
    });

    // Detect rapid table access
    let tableAccessCount = 0;
    const originalAddEventListener = table.addEventListener;

    table.addEventListener = function(event, handler, options) {
        tableAccessCount++;
        if (tableAccessCount > 10) {
            console.warn('Rapid table access detected - possible scraper');
            scrambleTableAttributes();
            tableAccessCount = 0;
        }
        return originalAddEventListener.call(this, event, handler, options);
    };
}

// === TABLE DATA OBFUSCATION ===
function obfuscateTableData() {
    // Add fake data attributes to confuse scrapers
    const tableRows = document.querySelectorAll('#contactTable tbody tr');
    tableRows.forEach((row, index) => {
        // Add fake data attributes
        row.setAttribute('data-fake-email', `fake${index}@example.com`);
        row.setAttribute('data-fake-phone', `******-${String(Math.random()).substring(2, 6)}`);
        row.setAttribute('data-fake-name', `Fake User ${index}`);

        // Add invisible fake content
        const fakeCells = row.querySelectorAll('td');
        fakeCells.forEach(cell => {
            if (!cell.querySelector('.fake-data')) {
                const fakeSpan = document.createElement('span');
                fakeSpan.className = 'fake-data';
                fakeSpan.style.display = 'none';
                fakeSpan.textContent = `fake-data-${Math.random()}`;
                cell.appendChild(fakeSpan);
            }
        });
    });
}

// === DETECT CHROME EXTENSIONS ===
function detectExtensions() {
    // Check for common extension injection patterns
    const suspiciousElements = document.querySelectorAll('[class*="extension"], [id*="extension"], [data-extension]');
    if (suspiciousElements.length > 0) {
        console.warn('Potential browser extension detected');
        activateEmergencyProtection();
    }

    // Monitor for injected scripts
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1 && (node.tagName === 'SCRIPT' || node.tagName === 'IFRAME')) {
                        console.warn('Suspicious script/iframe injection detected');
                        // Remove suspicious injected content
                        if (node.parentNode && !node.hasAttribute('data-allowed')) {
                            node.parentNode.removeChild(node);
                        }
                        // Activate emergency protection
                        activateEmergencyProtection();
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Check for common scraping extension signatures
    setTimeout(() => {
        if (window.chrome && window.chrome.runtime) {
            console.warn('Chrome extension environment detected');
            activateEmergencyProtection();
        }
    }, 2000);
}

// === EMERGENCY PROTECTION ===
function activateEmergencyProtection() {
    console.warn('EMERGENCY PROTECTION ACTIVATED');

    // Replace all table content with fake data immediately
    const tableBody = document.getElementById('tableBody');
    if (tableBody) {
        const rows = tableBody.querySelectorAll('tr:not(.decoy-row)');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, index) => {
                if (index > 0) { // Skip checkbox column
                    const dataTypes = ['name', 'phone', 'email', 'bio', 'facebook'];
                    const fakeData = generateFakeData(dataTypes[index - 1] || 'bio');

                    // Replace all content with fake data
                    const visibleData = cell.querySelector('.visible-data');
                    if (visibleData) {
                        visibleData.textContent = fakeData;
                    } else {
                        cell.textContent = fakeData;
                    }
                }
            });
        });
    }

    // Add warning message
    const warningDiv = document.createElement('div');
    warningDiv.style.position = 'fixed';
    warningDiv.style.top = '10px';
    warningDiv.style.right = '10px';
    warningDiv.style.background = 'red';
    warningDiv.style.color = 'white';
    warningDiv.style.padding = '10px';
    warningDiv.style.zIndex = '9999';
    warningDiv.style.fontSize = '12px';
    warningDiv.textContent = 'Security Alert: Unauthorized access detected';
    document.body.appendChild(warningDiv);

    setTimeout(() => {
        if (warningDiv.parentNode) {
            warningDiv.parentNode.removeChild(warningDiv);
        }
    }, 5000);
}

// === INIT USER ACTION TRACKING ===
function initUserActionTracking() {
    // Track legitimate user interactions
    document.addEventListener('click', (e) => {
        logUserAction('click', {
            target: e.target.tagName,
            x: e.clientX,
            y: e.clientY
        });
    });

    document.addEventListener('scroll', () => {
        logUserAction('scroll', {
            scrollY: window.scrollY
        });
    });

    document.addEventListener('keydown', (e) => {
        logUserAction('keyboard', {
            key: e.key,
            ctrlKey: e.ctrlKey
        });
    });

    document.addEventListener('mousemove', (e) => {
        // Log mouse movement occasionally (not every pixel)
        if (Math.random() < 0.01) { // 1% chance
            logUserAction('mousemove', {
                x: e.clientX,
                y: e.clientY
            });
        }
    });

    // Track form interactions
    document.addEventListener('input', (e) => {
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') {
            logUserAction('input', {
                type: e.target.type,
                id: e.target.id
            });
        }
    });
}

// === INIT ===
document.addEventListener("DOMContentLoaded", fetchUserLimits);
document.addEventListener("DOMContentLoaded", fetchContacts);
document.addEventListener("DOMContentLoaded", () => {
    // Initialize session info display
    const session = initializeSession();
    updateSessionInfo(session);
});