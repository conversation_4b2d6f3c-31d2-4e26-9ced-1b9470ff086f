<!DOCTYPE html>
<html lang="en">

<head>
    <?php include 'security.php'; ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- Advanced Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com;
        style-src 'self' 'unsafe-inline';
        img-src 'self' data:;
        connect-src 'self' http://localhost;
        font-src 'self';
        object-src 'none';
        media-src 'none';
        frame-src 'none';
        worker-src 'none';
        child-src 'none';
        form-action 'self';
        upgrade-insecure-requests;
        block-all-mixed-content;
    ">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="no-referrer">
    <meta http-equiv="Permissions-Policy" content="
        geolocation=(),
        microphone=(),
        camera=(),
        payment=(),
        usb=(),
        magnetometer=(),
        gyroscope=(),
        accelerometer=()
    ">

    <title>LeadFlow - Professional Contact Management</title>
    <link rel="stylesheet" href="styles.css">

    <link rel="apple-touch-icon" sizes="180x180" href="../icons/180-180.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../icons/32-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../icons/16-16.png">

    <!-- SheetJS for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

</head>

<body>
    <!-- Modern Header -->
    <header class="modern-header">
        <!-- Logo Section -->
        <div class="logo-section">
            <div class="logo-container">
                <img src="../icons/32-32.png" alt="LeadBaseAI Logo" class="logo-image">
            </div>
            <div class="brand-info">
                <h1 class="brand-title">LeadBaseAI</h1>
                <p class="brand-subtitle">AI-Powered Contact Intelligence</p>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="main-nav">
            <a href="#" class="nav-link active">
                <span class="nav-icon">🗃️</span>
                Database
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">💾</span>
                Saved
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">👤</span>
                Profile
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">🛠️</span>
                Tools
            </a>
        </nav>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>

        <!-- User Section -->
        <div class="user-section">
            <!-- Limits Display in Header -->
            <div class="header-limits">
                <div class="header-limit-item">
                    <span class="header-limit-label">Daily Limit</span>
                    <span class="header-limit-value" id="dailyLimit">...</span>
                </div>
                <div class="header-limit-item">
                    <span class="header-limit-label">Extra Limit</span>
                    <span class="header-limit-value" id="extraLimit">...</span>
                </div>
            </div>
            <div id="streakBox" class="streak-badge">
                <span class="streak-icon">🔥</span>
                <span class="streak-text">Streak: 0</span>
            </div>
            <div class="user-menu">
                <div id="userAvatar" class="user-avatar">
                    <span class="avatar-text">--</span>
                    <div class="avatar-status"></div>
                </div>
                <div class="profile-menu" id="profileMenu">
                    <!-- Dropdown by JS -->
                </div>
            </div>
        </div>
    </header>
    <style>
        /* Additional animations and enhancements */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Page load animations */
        .modern-header {
            animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .container {
            animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
        }

        .controls {
            animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
        }

        .table-container {
            animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
        }

        /* Floating elements */
        .floating-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .floating-bg::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.03) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translate(0, 0) rotate(0deg);
            }

            33% {
                transform: translate(30px, -30px) rotate(120deg);
            }

            66% {
                transform: translate(-20px, 20px) rotate(240deg);
            }
        }

        #contactTable th,
        #contactTable td {
            font-size: 13px;
            /* Adjust as needed: 12px, 13px, etc. */
            padding: 6px 10px;
            /* Optional: tighter padding */
        }

        /* Advanced Content Protection */
        * {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            -webkit-touch-callout: none !important;
            -webkit-tap-highlight-color: transparent !important;
            -webkit-appearance: none !important;
            appearance: none !important;
        }

        /* Prevent text selection highlighting */
        ::selection {
            background: transparent !important;
        }
        ::-moz-selection {
            background: transparent !important;
        }

        /* Allow selection for input fields only */
        input, textarea, select {
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            user-select: text !important;
        }

        /* Hide from print and screenshots */
        @media print {
            * {
                display: none !important;
            }
            body::before {
                content: "Printing is disabled for security reasons.";
                display: block !important;
                font-size: 24px;
                text-align: center;
                margin-top: 50px;
            }
        }

        /* Prevent drag and drop */
        * {
            -webkit-user-drag: none !important;
            -khtml-user-drag: none !important;
            -moz-user-drag: none !important;
            -o-user-drag: none !important;
        }

        /* Simple Content Protection */
        .fake-data, .decoy-data {
            display: none !important;
        }

        /* Print protection */
        @media print {
            * {
                display: none !important;
            }
            body::before {
                content: "Printing is disabled for security reasons.";
                display: block !important;
                font-size: 24px;
                text-align: center;
                margin-top: 50px;
            }
        }

        /* Protect table data */
        #contactTable {
            pointer-events: auto !important;
        }

        /* Anti-scraping measures */
        table::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            pointer-events: none;
            z-index: -1;
        }
    </style>



    <!-- Floating Background -->
    <div class="floating-bg"></div>

    <!-- Main Container -->
    <div class="container">


        <!-- Controls -->
        <div class="controls">
            <div class="controls-row">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search contacts..." id="searchInput">
                    <svg class="search-icon" width="16" height="16" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </div>
                <div class="filter-group">
                    <select class="filter-select" id="countryFilter">
                        <option value="" disabled selected>All Countries</option>
                        <option value="usa" selected>USA</option>
                        <option value="canada">Canada</option>
                        <option value="uk">United Kingdom</option>
                        <option value="india">India</option>
                        <option value="australia">Australia</option>
                        <option value="uae">UAE</option>
                    </select>
                    <select class="filter-select" id="specialFilter">
                        <option value="" disabled selected>Advance Filters</option>
                        <option value="none">None</option>
                        <option value="have_email">Have Email</option>
                        <option value="have_phone">Have Phone</option>
                        <option value="have_both">Have Both</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Bar -->
        <div class="action-bar" id="actionBar">
            <div class="selected-info">
                <span id="selectedCount">0</span> contacts selected
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="downloadBtn">Download</button>
                <button class="btn btn-secondary" id="saveBtn">Save</button>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">Available Data</h3>
                <div class="results-info">
                    <span>Total: <span id="totalResults">1,280</span> contacts</span>
                    <span id="sessionInfo" style="margin-left: 20px; color: #666; font-size: 12px;"></span>
                </div>
            </div>

            <table id="contactTable">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" class="checkbox" id="selectAll">
                        </th>
                        <th class="sortable">
                            Name
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Phone
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Email
                            <span class="sort-icon">↕</span>
                        </th>
                        <th>Bio</th>
                        <th>Facebook</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination"
                style="display: flex; justify-content: center; align-items: center; gap: 12px; margin-top: 20px;">
                <button id="prevBtn" class="page-btn"
                    style="padding: 6px 16px; border: none; background-color: #d1e9ff; color: #004c99; border-radius: 6px; cursor: pointer;">←
                    Prev</button>
                <div id="currentPageDisplay"
                    style="padding: 6px 14px; background-color: #e6f2ff; border-radius: 6px; font-weight: 500;">Page 1
                </div>
                <button id="nextBtn" class="page-btn"
                    style="padding: 6px 16px; border: none; background-color: #d1e9ff; color: #004c99; border-radius: 6px; cursor: pointer;">Next
                    →</button>
            </div>

        </div>
    </div>
    <script src="scripts.js"></script>
    <script>
        // Mobile menu toggle functionality
        document.addEventListener('DOMContentLoaded', function () {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mainNav = document.querySelector('.main-nav');

            mobileMenuToggle.addEventListener('click', function () {
                mobileMenuToggle.classList.toggle('active');
                mainNav.classList.toggle('active');

                // Prevent body scroll when menu is open
                if (mainNav.classList.contains('active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });

            // Close menu when clicking on nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function () {
                    mobileMenuToggle.classList.remove('active');
                    mainNav.classList.remove('active');
                    document.body.style.overflow = '';
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function (e) {
                if (!mainNav.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    mobileMenuToggle.classList.remove('active');
                    mainNav.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    </script>
</body>

</html>