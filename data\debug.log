[2025-07-23 19:19:01] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:19:01] DEBUG: Attempting leads database connection...
[2025-07-23 19:19:01] DEBUG: Leads database connection successful
[2025-07-23 19:19:01] DEBUG: Attempting users database connection...
[2025-07-23 19:19:01] DEBUG: Users database connection successful
[2025-07-23 19:19:01] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:19:01] DEBUG: Processing get_contacts action
[2025-07-23 19:19:01] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:19:01] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:19:01] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:19:01] DEBUG: Script execution completed
[2025-07-23 19:19:01] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:19:01] DEBUG: Attempting leads database connection...
[2025-07-23 19:19:01] DEBUG: Leads database connection successful
[2025-07-23 19:19:01] DEBUG: Attempting users database connection...
[2025-07-23 19:19:01] DEBUG: Users database connection successful
[2025-07-23 19:19:01] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:19:01] DEBUG: Processing get_contacts action
[2025-07-23 19:19:01] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:19:01] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:19:01] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:19:01] DEBUG: Script execution completed
[2025-07-23 19:26:00] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:26:00] DEBUG: Attempting leads database connection...
[2025-07-23 19:26:00] DEBUG: Leads database connection successful
[2025-07-23 19:26:00] DEBUG: Attempting users database connection...
[2025-07-23 19:26:00] DEBUG: Users database connection successful
[2025-07-23 19:26:00] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:26:00] DEBUG: Processing get_contacts action
[2025-07-23 19:26:00] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:26:00] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:26:00] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:26:00] DEBUG: Script execution completed
[2025-07-23 19:27:51] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:27:51] DEBUG: Attempting leads database connection...
[2025-07-23 19:27:51] DEBUG: Leads database connection successful
[2025-07-23 19:27:51] DEBUG: Attempting users database connection...
[2025-07-23 19:27:51] DEBUG: Users database connection successful
[2025-07-23 19:27:51] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:27:51] DEBUG: Processing get_contacts action
[2025-07-23 19:27:51] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:27:51] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:27:51] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:27:51] DEBUG: Script execution completed
[2025-07-23 19:28:10] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:28:10] DEBUG: Attempting leads database connection...
[2025-07-23 19:28:10] DEBUG: Leads database connection successful
[2025-07-23 19:28:10] DEBUG: Attempting users database connection...
[2025-07-23 19:28:10] DEBUG: Users database connection successful
[2025-07-23 19:28:10] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:28:10] DEBUG: Processing get_contacts action
[2025-07-23 19:28:10] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:28:10] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:28:10] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:28:10] DEBUG: Script execution completed
[2025-07-23 19:28:24] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_limits&email=aakashmishra2050880%40gmail.com
[2025-07-23 19:28:24] DEBUG: Attempting leads database connection...
[2025-07-23 19:28:24] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:28:24] DEBUG: Leads database connection successful
[2025-07-23 19:28:24] DEBUG: Attempting leads database connection...
[2025-07-23 19:28:24] DEBUG: Attempting users database connection...
[2025-07-23 19:28:24] DEBUG: Leads database connection successful
[2025-07-23 19:28:24] DEBUG: Attempting users database connection...
[2025-07-23 19:28:24] DEBUG: Users database connection successful
[2025-07-23 19:28:24] DEBUG: Action received: 'get_limits'
[2025-07-23 19:28:24] DEBUG: Users database connection successful
[2025-07-23 19:28:24] DEBUG: Processing get_limits action
[2025-07-23 19:28:24] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:28:24] DEBUG: Email parameter: '<EMAIL>'
[2025-07-23 19:28:24] DEBUG: Processing get_contacts action
[2025-07-23 19:28:24] DEBUG: Preparing SQL query for get_limits on users database
[2025-07-23 19:28:24] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:28:24] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:28:24] DEBUG: User exists check: {"count":1}
[2025-07-23 19:28:24] DEBUG: Raw database result: {"Dailylimit":100,"ExtraLimit":0}
[2025-07-23 19:28:24] DEBUG: Sending successful response: {"success":true,"limits":{"dailyLimit":100,"extraLimit":0}}
[2025-07-23 19:28:24] DEBUG: get_limits action completed
[2025-07-23 19:28:24] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:28:24] DEBUG: Script execution completed
[2025-07-23 19:29:57] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:29:57] DEBUG: Attempting leads database connection...
[2025-07-23 19:29:57] DEBUG: Leads database connection successful
[2025-07-23 19:29:57] DEBUG: Attempting users database connection...
[2025-07-23 19:29:57] DEBUG: Users database connection successful
[2025-07-23 19:29:57] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:29:57] DEBUG: Processing get_contacts action
[2025-07-23 19:29:57] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:29:57] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:29:57] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:29:57] DEBUG: Script execution completed
[2025-07-23 19:29:59] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:29:59] DEBUG: Attempting leads database connection...
[2025-07-23 19:29:59] DEBUG: Leads database connection successful
[2025-07-23 19:29:59] DEBUG: Attempting users database connection...
[2025-07-23 19:29:59] DEBUG: Users database connection successful
[2025-07-23 19:29:59] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:29:59] DEBUG: Processing get_contacts action
[2025-07-23 19:29:59] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:29:59] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:29:59] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:29:59] DEBUG: Script execution completed
[2025-07-23 19:30:00] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:30:00] DEBUG: Attempting leads database connection...
[2025-07-23 19:30:00] DEBUG: Leads database connection successful
[2025-07-23 19:30:00] DEBUG: Attempting users database connection...
[2025-07-23 19:30:00] DEBUG: Users database connection successful
[2025-07-23 19:30:00] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:30:00] DEBUG: Processing get_contacts action
[2025-07-23 19:30:00] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:30:00] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:30:00] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:30:00] DEBUG: Script execution completed
[2025-07-23 19:31:48] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_limits&email=aakashmishra2050880%40gmail.com
[2025-07-23 19:31:48] DEBUG: Attempting leads database connection...
[2025-07-23 19:31:48] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:31:48] DEBUG: Attempting leads database connection...
[2025-07-23 19:31:48] DEBUG: Leads database connection successful
[2025-07-23 19:31:48] DEBUG: Attempting users database connection...
[2025-07-23 19:31:48] DEBUG: Users database connection successful
[2025-07-23 19:31:48] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:31:48] DEBUG: Leads database connection successful
[2025-07-23 19:31:48] DEBUG: Attempting users database connection...
[2025-07-23 19:31:48] DEBUG: Processing get_contacts action
[2025-07-23 19:31:48] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:31:48] DEBUG: Users database connection successful
[2025-07-23 19:31:48] DEBUG: Action received: 'get_limits'
[2025-07-23 19:31:48] DEBUG: Processing get_limits action
[2025-07-23 19:31:48] DEBUG: Email parameter: '<EMAIL>'
[2025-07-23 19:31:48] DEBUG: Preparing SQL query for get_limits on users database
[2025-07-23 19:31:48] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:31:48] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:31:48] DEBUG: Script execution completed
[2025-07-23 19:31:48] DEBUG: User exists check: {"count":1}
[2025-07-23 19:31:49] DEBUG: Raw database result: {"Dailylimit":100,"ExtraLimit":0}
[2025-07-23 19:31:49] DEBUG: Sending successful response: {"success":true,"limits":{"dailyLimit":100,"extraLimit":0}}
[2025-07-23 19:31:49] DEBUG: get_limits action completed
[2025-07-23 19:31:54] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=canada
[2025-07-23 19:31:54] DEBUG: Attempting leads database connection...
[2025-07-23 19:31:54] DEBUG: Leads database connection successful
[2025-07-23 19:31:54] DEBUG: Attempting users database connection...
[2025-07-23 19:31:54] DEBUG: Users database connection successful
[2025-07-23 19:31:54] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:31:54] DEBUG: Processing get_contacts action
[2025-07-23 19:31:54] DEBUG: Country/table: 'canada', Special filter: ''
[2025-07-23 19:31:54] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `canada`
        
        ORDER BY id DESC
[2025-07-23 19:31:54] DEBUG: Contacts fetched successfully. Count: 0
[2025-07-23 19:31:54] DEBUG: Script execution completed
[2025-07-23 19:31:59] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=india
[2025-07-23 19:31:59] DEBUG: Attempting leads database connection...
[2025-07-23 19:31:59] DEBUG: Leads database connection successful
[2025-07-23 19:31:59] DEBUG: Attempting users database connection...
[2025-07-23 19:31:59] DEBUG: Users database connection successful
[2025-07-23 19:31:59] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:31:59] DEBUG: Processing get_contacts action
[2025-07-23 19:31:59] DEBUG: Country/table: 'india', Special filter: ''
[2025-07-23 19:31:59] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `india`
        
        ORDER BY id DESC
[2025-07-23 19:31:59] DEBUG: Contacts fetched successfully. Count: 1214
[2025-07-23 19:31:59] DEBUG: Script execution completed
[2025-07-23 19:33:43] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:33:43] DEBUG: Attempting leads database connection...
[2025-07-23 19:33:43] DEBUG: Leads database connection successful
[2025-07-23 19:33:43] DEBUG: Attempting users database connection...
[2025-07-23 19:33:43] DEBUG: Users database connection successful
[2025-07-23 19:33:43] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:33:43] DEBUG: Processing get_contacts action
[2025-07-23 19:33:43] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:33:43] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:33:43] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:33:43] DEBUG: Script execution completed
[2025-07-23 19:33:46] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:33:46] DEBUG: Attempting leads database connection...
[2025-07-23 19:33:46] DEBUG: Leads database connection successful
[2025-07-23 19:33:46] DEBUG: Attempting users database connection...
[2025-07-23 19:33:46] DEBUG: Users database connection successful
[2025-07-23 19:33:46] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:33:46] DEBUG: Processing get_contacts action
[2025-07-23 19:33:46] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:33:46] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:33:46] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:33:46] DEBUG: Script execution completed
[2025-07-23 19:35:27] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:35:27] DEBUG: Attempting leads database connection...
[2025-07-23 19:35:27] DEBUG: Leads database connection successful
[2025-07-23 19:35:27] DEBUG: Attempting users database connection...
[2025-07-23 19:35:27] DEBUG: Users database connection successful
[2025-07-23 19:35:27] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:35:27] DEBUG: Processing get_contacts action
[2025-07-23 19:35:27] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:35:27] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:35:27] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:35:27] DEBUG: Script execution completed
[2025-07-23 19:36:03] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_limits&email=aakashmishra2050880%40gmail.com
[2025-07-23 19:36:03] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:03] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:36:03] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:03] DEBUG: Leads database connection successful
[2025-07-23 19:36:03] DEBUG: Attempting users database connection...
[2025-07-23 19:36:03] DEBUG: Users database connection successful
[2025-07-23 19:36:03] DEBUG: Action received: 'get_limits'
[2025-07-23 19:36:03] DEBUG: Processing get_limits action
[2025-07-23 19:36:03] DEBUG: Email parameter: '<EMAIL>'
[2025-07-23 19:36:03] DEBUG: Preparing SQL query for get_limits on users database
[2025-07-23 19:36:03] DEBUG: User exists check: {"count":1}
[2025-07-23 19:36:03] DEBUG: Raw database result: {"Dailylimit":100,"ExtraLimit":0}
[2025-07-23 19:36:03] DEBUG: Sending successful response: {"success":true,"limits":{"dailyLimit":100,"extraLimit":0}}
[2025-07-23 19:36:03] DEBUG: get_limits action completed
[2025-07-23 19:36:03] DEBUG: Leads database connection successful
[2025-07-23 19:36:03] DEBUG: Attempting users database connection...
[2025-07-23 19:36:03] DEBUG: Users database connection successful
[2025-07-23 19:36:03] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:36:03] DEBUG: Processing get_contacts action
[2025-07-23 19:36:03] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:36:03] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:36:03] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:36:03] DEBUG: Script execution completed
[2025-07-23 19:36:37] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:36:37] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:37] DEBUG: Leads database connection successful
[2025-07-23 19:36:37] DEBUG: Attempting users database connection...
[2025-07-23 19:36:37] DEBUG: Users database connection successful
[2025-07-23 19:36:37] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:36:37] DEBUG: Processing get_contacts action
[2025-07-23 19:36:37] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:36:37] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:36:37] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:36:37] DEBUG: Script execution completed
[2025-07-23 19:36:42] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:36:42] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:42] DEBUG: Leads database connection successful
[2025-07-23 19:36:42] DEBUG: Attempting users database connection...
[2025-07-23 19:36:42] DEBUG: Users database connection successful
[2025-07-23 19:36:42] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:36:42] DEBUG: Processing get_contacts action
[2025-07-23 19:36:42] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:36:42] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:36:42] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:36:42] DEBUG: Script execution completed
[2025-07-23 19:36:50] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_limits&email=aakashmishra2050880%40gmail.com
[2025-07-23 19:36:50] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:50] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:36:50] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:50] DEBUG: Leads database connection successful
[2025-07-23 19:36:50] DEBUG: Leads database connection successful
[2025-07-23 19:36:50] DEBUG: Attempting users database connection...
[2025-07-23 19:36:50] DEBUG: Attempting users database connection...
[2025-07-23 19:36:50] DEBUG: Users database connection successful
[2025-07-23 19:36:50] DEBUG: Users database connection successful
[2025-07-23 19:36:50] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:36:50] DEBUG: Action received: 'get_limits'
[2025-07-23 19:36:50] DEBUG: Processing get_contacts action
[2025-07-23 19:36:50] DEBUG: Processing get_limits action
[2025-07-23 19:36:50] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:36:50] DEBUG: Email parameter: '<EMAIL>'
[2025-07-23 19:36:50] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:36:50] DEBUG: Preparing SQL query for get_limits on users database
[2025-07-23 19:36:50] DEBUG: User exists check: {"count":1}
[2025-07-23 19:36:50] DEBUG: Raw database result: {"Dailylimit":100,"ExtraLimit":0}
[2025-07-23 19:36:50] DEBUG: Sending successful response: {"success":true,"limits":{"dailyLimit":100,"extraLimit":0}}
[2025-07-23 19:36:50] DEBUG: get_limits action completed
[2025-07-23 19:36:50] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:36:50] DEBUG: Script execution completed
[2025-07-23 19:36:58] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_limits&email=aakashmishra2050880%40gmail.com
[2025-07-23 19:36:58] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:58] DEBUG: Leads database connection successful
[2025-07-23 19:36:58] DEBUG: Attempting users database connection...
[2025-07-23 19:36:58] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:36:58] DEBUG: Attempting leads database connection...
[2025-07-23 19:36:58] DEBUG: Leads database connection successful
[2025-07-23 19:36:58] DEBUG: Attempting users database connection...
[2025-07-23 19:36:58] DEBUG: Users database connection successful
[2025-07-23 19:36:58] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:36:58] DEBUG: Processing get_contacts action
[2025-07-23 19:36:58] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:36:58] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:36:58] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:36:58] DEBUG: Script execution completed
[2025-07-23 19:36:58] DEBUG: Users database connection successful
[2025-07-23 19:36:58] DEBUG: Action received: 'get_limits'
[2025-07-23 19:36:58] DEBUG: Processing get_limits action
[2025-07-23 19:36:58] DEBUG: Email parameter: '<EMAIL>'
[2025-07-23 19:36:58] DEBUG: Preparing SQL query for get_limits on users database
[2025-07-23 19:36:58] DEBUG: User exists check: {"count":1}
[2025-07-23 19:36:58] DEBUG: Raw database result: {"Dailylimit":100,"ExtraLimit":0}
[2025-07-23 19:36:58] DEBUG: Sending successful response: {"success":true,"limits":{"dailyLimit":100,"extraLimit":0}}
[2025-07-23 19:36:58] DEBUG: get_limits action completed
[2025-07-23 19:37:06] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_contacts&country=usa
[2025-07-23 19:37:06] DEBUG: Script started. Request: /leadbase/Data/data.php?action=get_limits&email=aakashmishra2050880%40gmail.com
[2025-07-23 19:37:06] DEBUG: Attempting leads database connection...
[2025-07-23 19:37:06] DEBUG: Attempting leads database connection...
[2025-07-23 19:37:06] DEBUG: Leads database connection successful
[2025-07-23 19:37:06] DEBUG: Attempting users database connection...
[2025-07-23 19:37:06] DEBUG: Leads database connection successful
[2025-07-23 19:37:06] DEBUG: Users database connection successful
[2025-07-23 19:37:06] DEBUG: Attempting users database connection...
[2025-07-23 19:37:06] DEBUG: Action received: 'get_limits'
[2025-07-23 19:37:06] DEBUG: Processing get_limits action
[2025-07-23 19:37:06] DEBUG: Email parameter: '<EMAIL>'
[2025-07-23 19:37:06] DEBUG: Preparing SQL query for get_limits on users database
[2025-07-23 19:37:06] DEBUG: Users database connection successful
[2025-07-23 19:37:06] DEBUG: Action received: 'get_contacts'
[2025-07-23 19:37:06] DEBUG: Processing get_contacts action
[2025-07-23 19:37:06] DEBUG: Country/table: 'usa', Special filter: ''
[2025-07-23 19:37:06] DEBUG: Final SQL query: SELECT name, phone, email, bio, fb_url 
        FROM `usa`
        
        ORDER BY id DESC
[2025-07-23 19:37:06] DEBUG: User exists check: {"count":1}
[2025-07-23 19:37:06] DEBUG: Contacts fetched successfully. Count: 1280
[2025-07-23 19:37:06] DEBUG: Raw database result: {"Dailylimit":100,"ExtraLimit":0}
[2025-07-23 19:37:06] DEBUG: Sending successful response: {"success":true,"limits":{"dailyLimit":100,"extraLimit":0}}
[2025-07-23 19:37:06] DEBUG: get_limits action completed
[2025-07-23 19:37:06] DEBUG: Script execution completed
