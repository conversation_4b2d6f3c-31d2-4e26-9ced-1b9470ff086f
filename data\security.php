<?php
// === ADVANCED SECURITY HEADERS ===
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' http://localhost; font-src 'self'; object-src 'none'; media-src 'none'; frame-src 'none'; worker-src 'none'; child-src 'none'; form-action 'self'; upgrade-insecure-requests; block-all-mixed-content;");

header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: no-referrer");
header("Permissions-Policy: geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()");

// Anti-caching headers
header("Cache-Control: no-cache, no-store, must-revalidate, private");
header("Pragma: no-cache");
header("Expires: 0");

// Additional security headers
header("Strict-Transport-Security: max-age=31536000; includeSubDomains");
header("Feature-Policy: geolocation 'none'; microphone 'none'; camera 'none'");

// Extension blocking headers
header("Cross-Origin-Embedder-Policy: require-corp");
header("Cross-Origin-Opener-Policy: same-origin");
header("Cross-Origin-Resource-Policy: same-origin");

// Detect and block suspicious user agents
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$suspicious_patterns = [
    'headless', 'phantom', 'selenium', 'webdriver', 
    'bot', 'crawler', 'spider', 'scraper', 'automation',
    'chrome-extension', 'extension'
];

foreach ($suspicious_patterns as $pattern) {
    if (stripos($user_agent, $pattern) !== false) {
        http_response_code(403);
        die('Access Denied: Automated access detected');
    }
}

// Check for extension-specific headers
$extension_headers = [
    'HTTP_X_CHROME_EXTENSION',
    'HTTP_X_EXTENSION_ID',
    'HTTP_X_REQUESTED_WITH'
];

foreach ($extension_headers as $header) {
    if (isset($_SERVER[$header])) {
        http_response_code(403);
        die('Access Denied: Extension access blocked');
    }
}

// Rate limiting (basic)
session_start();
$current_time = time();
$requests_key = 'requests_' . $_SERVER['REMOTE_ADDR'];

if (!isset($_SESSION[$requests_key])) {
    $_SESSION[$requests_key] = [];
}

// Clean old requests (older than 1 minute)
$_SESSION[$requests_key] = array_filter($_SESSION[$requests_key], function($timestamp) use ($current_time) {
    return ($current_time - $timestamp) < 60;
});

// Add current request
$_SESSION[$requests_key][] = $current_time;

// Check if too many requests
if (count($_SESSION[$requests_key]) > 100) { // Max 100 requests per minute
    http_response_code(429);
    die('Rate limit exceeded');
}

// Log suspicious activity
function logSuspiciousActivity($reason, $details = '') {
    $log_entry = date('Y-m-d H:i:s') . " - SECURITY ALERT: $reason - IP: " . $_SERVER['REMOTE_ADDR'] . " - UA: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . " - Details: $details\n";
    file_put_contents('security.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Check for rapid requests (potential scraping)
if (count($_SESSION[$requests_key]) > 50) {
    logSuspiciousActivity('Rapid requests detected', 'Requests in last minute: ' . count($_SESSION[$requests_key]));
}

// Check for suspicious referers
$referer = $_SERVER['HTTP_REFERER'] ?? '';
if (!empty($referer) && !preg_match('/^https?:\/\/(localhost|127\.0\.0\.1|leadbaseai\.in)/', $referer)) {
    logSuspiciousActivity('Suspicious referer', $referer);
    http_response_code(403);
    die('Access Denied: Invalid referer');
}

// JavaScript integrity check
function generateIntegrityToken() {
    return hash('sha256', 'LeadBaseSecure2024' . date('Y-m-d-H'));
}

// Set integrity token for client-side verification
$integrity_token = generateIntegrityToken();
setcookie('integrity_token', $integrity_token, time() + 3600, '/', '', false, true);

// Output security JavaScript
echo "
<script>
// Server-side security validation
(function() {
    'use strict';
    
    // Verify integrity token
    const expectedToken = '$integrity_token';
    const cookieToken = document.cookie.split(';').find(row => row.startsWith('integrity_token='));
    
    if (!cookieToken || cookieToken.split('=')[1] !== expectedToken) {
        console.error('Security token mismatch');
        document.body.innerHTML = '<h1>Security Error</h1>';
        return;
    }
    
    // Additional runtime checks
    const securityChecks = {
        checkExtensions: function() {
            // More aggressive extension detection
            if (window.chrome && window.chrome.runtime && window.chrome.runtime.onMessage) {
                this.blockAccess('Chrome extension detected');
                return false;
            }
            
            // Check for common extension global variables
            const extensionVars = ['__REACT_DEVTOOLS_GLOBAL_HOOK__', '__VUE_DEVTOOLS_GLOBAL_HOOK__', 'webpackJsonp'];
            for (const varName of extensionVars) {
                if (window[varName]) {
                    this.blockAccess('Developer extension detected: ' + varName);
                    return false;
                }
            }
            
            return true;
        },
        
        blockAccess: function(reason) {
            console.error('Access blocked:', reason);
            document.body.innerHTML = `
                <div style='text-align:center;margin-top:200px;color:red;font-size:24px;'>
                    <h1>🚫 ACCESS DENIED</h1>
                    <p>Reason: \${reason}</p>
                    <p>Please disable all browser extensions and refresh the page.</p>
                </div>
            `;
            
            // Send alert to server
            fetch('security.php?alert=' + encodeURIComponent(reason), {method: 'POST'});
        },
        
        init: function() {
            // Run checks immediately
            if (!this.checkExtensions()) return;
            
            // Run periodic checks
            setInterval(() => {
                this.checkExtensions();
            }, 5000);
        }
    };
    
    // Initialize security checks
    securityChecks.init();
})();
</script>
";

// Handle security alerts
if (isset($_GET['alert'])) {
    logSuspiciousActivity('Client-side security alert', $_GET['alert']);
    http_response_code(200);
    exit;
}
?>
