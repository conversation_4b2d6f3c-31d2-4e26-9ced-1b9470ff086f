<?php
// === SECURITY API ENDPOINT ===
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// === SECURITY HEADERS FOR API ===
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: no-referrer");

// Anti-caching headers
header("Cache-Control: no-cache, no-store, must-revalidate, private");
header("Pragma: no-cache");
header("Expires: 0");

// Detect and block suspicious user agents
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$suspicious_patterns = [
    'headless', 'phantom', 'selenium', 'webdriver', 
    'bot', 'crawler', 'spider', 'scraper', 'automation',
    'chrome-extension', 'extension'
];

foreach ($suspicious_patterns as $pattern) {
    if (stripos($user_agent, $pattern) !== false) {
        http_response_code(403);
        die('Access Denied: Automated access detected');
    }
}

// Check for extension-specific headers
$extension_headers = [
    'HTTP_X_CHROME_EXTENSION',
    'HTTP_X_EXTENSION_ID',
    'HTTP_X_REQUESTED_WITH'
];

foreach ($extension_headers as $header) {
    if (isset($_SERVER[$header])) {
        http_response_code(403);
        die('Access Denied: Extension access blocked');
    }
}

// Rate limiting (basic)
session_start();
$current_time = time();
$requests_key = 'requests_' . $_SERVER['REMOTE_ADDR'];

if (!isset($_SESSION[$requests_key])) {
    $_SESSION[$requests_key] = [];
}

// Clean old requests (older than 1 minute)
$_SESSION[$requests_key] = array_filter($_SESSION[$requests_key], function($timestamp) use ($current_time) {
    return ($current_time - $timestamp) < 60;
});

// Add current request
$_SESSION[$requests_key][] = $current_time;

// Check if too many requests
if (count($_SESSION[$requests_key]) > 100) { // Max 100 requests per minute
    http_response_code(429);
    die('Rate limit exceeded');
}

// Log suspicious activity
function logSuspiciousActivity($reason, $details = '') {
    $log_entry = date('Y-m-d H:i:s') . " - SECURITY ALERT: $reason - IP: " . $_SERVER['REMOTE_ADDR'] . " - UA: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . " - Details: $details\n";
    file_put_contents('security.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Check for rapid requests (potential scraping)
if (count($_SESSION[$requests_key]) > 50) {
    logSuspiciousActivity('Rapid requests detected', 'Requests in last minute: ' . count($_SESSION[$requests_key]));
}

// Check for suspicious referers
$referer = $_SERVER['HTTP_REFERER'] ?? '';
if (!empty($referer) && !preg_match('/^https?:\/\/(localhost|127\.0\.0\.1|leadbaseai\.in)/', $referer)) {
    logSuspiciousActivity('Suspicious referer', $referer);
    http_response_code(403);
    die('Access Denied: Invalid referer');
}

// JavaScript integrity check
function generateIntegrityToken() {
    return hash('sha256', 'LeadBaseSecure2024' . date('Y-m-d-H'));
}

// Set integrity token for client-side verification
$integrity_token = generateIntegrityToken();
setcookie('integrity_token', $integrity_token, time() + 3600, '/', '', false, true);

// Output security JavaScript
echo "
<script>
// Server-side security validation
(function() {
    'use strict';
    
    // Verify integrity token
    const expectedToken = '$integrity_token';
    const cookieToken = document.cookie.split(';').find(row => row.startsWith('integrity_token='));
    
    if (!cookieToken || cookieToken.split('=')[1] !== expectedToken) {
        console.error('Security token mismatch');
        document.body.innerHTML = '<h1>Security Error</h1>';
        return;
    }
    
    // Additional runtime checks
    const securityChecks = {
        checkExtensions: function() {
            // More aggressive extension detection
            if (window.chrome && window.chrome.runtime && window.chrome.runtime.onMessage) {
                this.blockAccess('Chrome extension detected');
                return false;
            }
            
            // Check for common extension global variables
            const extensionVars = ['__REACT_DEVTOOLS_GLOBAL_HOOK__', '__VUE_DEVTOOLS_GLOBAL_HOOK__', 'webpackJsonp'];
            for (const varName of extensionVars) {
                if (window[varName]) {
                    this.blockAccess('Developer extension detected: ' + varName);
                    return false;
                }
            }
            
            return true;
        },
        
        blockAccess: function(reason) {
            console.error('Access blocked:', reason);
            document.body.innerHTML = `
                <div style='text-align:center;margin-top:200px;color:red;font-size:24px;'>
                    <h1>🚫 ACCESS DENIED</h1>
                    <p>Reason: \${reason}</p>
                    <p>Please disable all browser extensions and refresh the page.</p>
                </div>
            `;
            
            // Send alert to server
            fetch('security.php?alert=' + encodeURIComponent(reason), {method: 'POST'});
        },
        
        init: function() {
            // Run checks immediately
            if (!this.checkExtensions()) return;
            
            // Run periodic checks
            setInterval(() => {
                this.checkExtensions();
            }, 5000);
        }
    };
    
    // Initialize security checks
    securityChecks.init();
})();
</script>
";

// === API ENDPOINTS ===
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'check_security':
        echo json_encode([
            'status' => 'ok',
            'timestamp' => time(),
            'security_level' => 'high',
            'extensions_blocked' => true
        ]);
        break;

    case 'log_alert':
        $alert = $_POST['alert'] ?? $_GET['alert'] ?? '';
        if ($alert) {
            logSuspiciousActivity('Client-side security alert', $alert);
            echo json_encode(['status' => 'logged']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'No alert data']);
        }
        break;

    case 'validate_session':
        // Simple session validation
        session_start();
        $valid = isset($_SESSION['security_token']) &&
                 $_SESSION['security_token'] === hash('sha256', 'LeadBaseSecure2024' . date('Y-m-d'));

        if (!$valid) {
            $_SESSION['security_token'] = hash('sha256', 'LeadBaseSecure2024' . date('Y-m-d'));
        }

        echo json_encode([
            'valid' => true,
            'token' => $_SESSION['security_token'],
            'timestamp' => time()
        ]);
        break;

    default:
        echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
        break;
}
?>
